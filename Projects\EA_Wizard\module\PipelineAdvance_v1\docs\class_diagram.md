# PipelineAdvance_v1 模組類別圖

PipelineAdvance_v1 模組是一個簡化的流水線處理架構，實現了組合模式和模板方法模式，專注於交易流水線的管理和執行。

## 📁 模組結構

```
PipelineAdvance_v1/
├── interface/                                  # 介面定義資料夾
│   ├── ITradingPipeline.mqh                   # 交易流水線介面
│   └── ITradingPipelineDriver.mqh             # 交易流水線驅動器介面
├── docs/                                      # 文檔目錄
│   ├── class_diagram.md                       # 本類別圖文檔
│   ├── architecture.md                        # 架構設計文檔
│   └── ...                                    # 其他文檔
├── test/                                      # 測試目錄
│   ├── RunAllTests.mqh                        # 主測試入口
│   ├── TestFramework.mqh                      # 測試框架
│   ├── unit/                                  # 單元測試
│   └── integration/                           # 整合測試
├── examples/                                  # 使用示例
│   └── TradingPipelineExplorerExample.mqh     # 探索器使用示例
├── TradingPipeline.mqh                        # 抽象基類
├── TradingPipelineContainerBase.mqh           # 容器抽象基類
├── TradingPipelineContainer.mqh               # 統一容器類（包含EventPipeline和StagePipeline）
├── TradingPipelineContainerManager.mqh        # 容器管理器
├── TradingPipelineDriverBase.mqh              # 驅動器抽象基類
├── TradingPipelineDriver.mqh                  # 驅動器（單例）
├── TradingPipelineRegistry.mqh                # 註冊器
├── TradingPipelineExplorer.mqh                # 探索器
├── TradingController.mqh                      # EA 生命週期控制器
├── MainPipeline.mqh                           # 主流水線抽象類
├── PipelineResult.mqh                         # 結果類
├── TradingEvent.mqh                           # 事件和枚舉定義
└── feature/                                   # 功能擴展目錄
    ├── TradingPipelineContainerLoggerDecorator.mqh  # 容器日誌裝飾者
    └── TradingPipelineDriverLoggerDecorator.mqh     # 驅動器日誌裝飾者
```

## 核心概念

- **ITradingPipeline**: 交易流水線介面，定義了基本的流水線操作（位於 interface/ 資料夾）
- **ITradingPipelineDriver**: 交易流水線驅動器介面，定義了驅動器的核心功能（位於 interface/ 資料夾）
- **TradingPipeline**: 抽象基類，實現了 ITradingPipeline 介面的基本功能，包含 Driver 和 Stage
- **TradingPipelineContainerBase**: 容器抽象基類，提供容器管理的基本功能和模板方法模式
- **TradingPipelineContainer**: 統一的容器類，繼承自 TradingPipelineContainerBase，合併了原有 CompositePipeline 和 PipelineGroup 的功能
- **TradingPipelineContainerManager**: 簡化的管理器類別，使用動態容器管理多個 TradingPipelineContainer
- **TradingPipelineDriverBase**: 驅動器抽象基類，實現 ITradingPipelineDriver 介面，提供模板方法模式和基本功能
- **TradingPipelineDriver**: 單例驅動器，繼承自 TradingPipelineDriverBase，負責初始化和控制主要模組
- **TradingPipelineDriverLoggerDecorator**: 驅動器日誌裝飾者，為驅動器添加詳細的日誌記錄功能
- **TradingPipelineContainerLoggerDecorator**: 容器日誌裝飾者，為容器添加詳細的日誌記錄功能
- **TradingPipelineExplorer**: 流水線探索器，提供豐富的查詢功能，根據交易階段或事件查找流水線
- **TradingPipelineRegistry**: 流水線註冊器，負責管理和註冊交易階段和事件到容器管理器
- **TradingController**: EA 生命週期控制器，作為 EA 的統一入口點，管理 OnInit、OnTick、OnDeinit 生命週期
- **EventPipeline**: 事件特定的流水線容器，繼承自 TradingPipelineContainer
- **StagePipeline**: 階段特定的流水線容器，繼承自 TradingPipelineContainer
- **PipelineResult**: 結果類，用於存儲流水線執行結果

## 🆕 最新變更 (v1.2)

### 介面抽象化

- **新增 interface/ 資料夾**：將所有介面定義移至專門的資料夾中
- **ITradingPipelineDriver 介面**：新增驅動器介面，遵循介面隔離原則
- **TradingPipeline 更新**：使用 `ITradingPipelineDriver*` 替代具體的驅動器類型
- **不可變設計**：介面不包含 setter 方法，確保狀態穩定

### 專門化容器

- **EventPipeline**：事件特定的流水線容器，包含 `ENUM_TRADING_EVENT` 成員
- **StagePipeline**：階段特定的流水線容器，包含 `ENUM_TRADING_STAGE` 成員
- **類型安全**：通過繼承類提供更強的類型安全和專門化方法

### 架構優化

- **單例驅動器**：TradingPipelineDriver 提供統一的系統入口點
- **自動初始化**：驅動器自動管理所有核心組件的依賴關係
- **簡化使用**：MainPipeline 自動注入驅動器實例，無需手動傳遞

## 類別圖

為了更好的可讀性，我們將類別圖分解為四個大的部分，保留所有完整的成員和方法：

### 1. 核心架構類別圖（完整版）

```mermaid
classDiagram
    %% 核心介面
    class ITradingPipeline {
        <<interface>>
        +Execute() void
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +Restore() void
    }

    %% 驅動器介面
    class ITradingPipelineDriver {
        <<interface>>
        +Initialize() bool
        +GetManager() TradingPipelineContainerManager*
        +GetRegistry() TradingPipelineRegistry*
        +GetExplorer() TradingPipelineExplorer*
        +IsInitialized() bool
        +GetName() string
        +GetType() string
        +SetupDefaultConfiguration() bool
        +Cleanup() void
    }

    %% 抽象基類
    class TradingPipeline {
        <<abstract>>
        #string m_name
        #string m_type
        #bool m_executed
        #ENUM_TRADING_STAGE m_stage
        #ITradingPipelineDriver* m_driver
        #PipelineResult* m_last_result
        +TradingPipeline(string name, string type, ENUM_TRADING_STAGE stage, ITradingPipelineDriver* driver = NULL)
        +~TradingPipeline()
        +Execute() void
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +Restore() void
        +GetStage() ENUM_TRADING_STAGE
        +GetDriver() ITradingPipelineDriver*
        +GetResult() PipelineResult*
        #Main() void*
    }

    %% 容器抽象基類
    class TradingPipelineContainerBase {
        <<abstract>>
        #string m_name
        #string m_type
        #string m_description
        #bool m_executed
        #bool m_isEnabled
        #Vector~ITradingPipeline*~ m_pipelines
        #bool m_owned
        #int m_maxPipelines
        #PipelineResult* m_last_result
        +TradingPipelineContainerBase(string name, string description = "", string type = "TradingPipelineContainerBase", bool owned = false, int maxPipelines = 50)
        +~TradingPipelineContainerBase()
        +Execute() void
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +Restore() void
        +AddPipeline(ITradingPipeline* pipeline) bool
        +RemovePipeline(ITradingPipeline* pipeline) bool
        +GetPipeline(int index, ITradingPipeline* defaultValue = NULL) ITradingPipeline*
        +Clear() void
        +GetPipelineCount() int
        +GetMaxPipelines() int
        +HasPipeline(ITradingPipeline* pipeline) bool
        +GetDescription() string
        +SetEnabled(bool enabled) void
        +IsEnabled() bool
        +GetResult() PipelineResult*
        +GetStatusInfo() string
        +IsEmpty() bool
        +IsFull() bool
        +GetAllPipelines(ITradingPipeline* &pipelines[]) int
        #SetResult(bool success, string message, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO) void
        #PreExecuteCheck() bool
        #ExecuteInternal() void*
        #PostExecuteProcess() void
    }

    %% 統一的交易流水線容器
    class TradingPipelineContainer {
        +TradingPipelineContainer(string name, string description = "", string type = "TradingPipelineContainer", bool owned = false, int maxPipelines = 50)
        +~TradingPipelineContainer()
        #ExecuteInternal() void
    }

    %% 事件流水線容器
    class EventPipeline {
        -ENUM_TRADING_EVENT m_event
        +EventPipeline(string name, ENUM_TRADING_EVENT event, string description = "", string type = "EventPipeline", bool owned = false, int maxPipelines = 50)
        +~EventPipeline()
        +GetEvent() ENUM_TRADING_EVENT
    }

    %% 階段流水線容器
    class StagePipeline {
        -ENUM_TRADING_STAGE m_stage
        +StagePipeline(string name, ENUM_TRADING_STAGE stage, string description = "", string type = "StagePipeline", bool owned = false, int maxPipelines = 50)
        +~StagePipeline()
        +GetStage() ENUM_TRADING_STAGE
    }

    %% 交易流水線容器日誌裝飾者
    class TradingPipelineContainerLoggerDecorator {
        -CFileLog* m_logger
        -bool m_enableDetailedLogging
        +TradingPipelineContainerLoggerDecorator(string name, string description, CFileLog* logger, string type = "TradingPipelineContainerLoggerDecorator", bool owned = false, int maxPipelines = 50, bool enableDetailedLogging = true)
        +~TradingPipelineContainerLoggerDecorator()
        +Execute() void
        +SetDetailedLogging(bool enabled) void
        +GetLogger() CFileLog*
        +IsDetailedLoggingEnabled() bool
        #PreExecuteCheck() bool
        #ExecuteInternal() void
        #PostExecuteProcess() void
        -LogConstructor() void
        -LogDestructor() void
        -LogInfo(string message) void
        -LogDebug(string message) void
        -LogWarning(string message) void
        -LogError(string message) void
        -LogExecutionDetails() void
    }

    %% 關係
    ITradingPipeline <|.. TradingPipeline : implements
    ITradingPipeline <|.. TradingPipelineContainerBase : implements
    TradingPipelineContainerBase <|-- TradingPipelineContainer : extends
    TradingPipelineContainerBase <|-- TradingPipelineContainerLoggerDecorator : extends
    TradingPipelineContainer <|-- EventPipeline : extends
    TradingPipelineContainer <|-- StagePipeline : extends
    TradingPipelineContainerBase o-- ITradingPipeline : contains
    TradingPipeline --> ITradingPipelineDriver : uses
    TradingPipelineContainerLoggerDecorator --> CFileLog : uses
```

### 2. 管理器和輔助類別圖（完整版）

```mermaid
classDiagram
    %% 交易流水線驅動器基類
    class TradingPipelineDriverBase {
        <<abstract>>
        #TradingPipelineContainerManager* m_manager
        #TradingPipelineRegistry* m_registry
        #TradingPipelineExplorer* m_explorer
        #bool m_isInitialized
        #string m_name
        #string m_type
        #PipelineResult* m_last_result
        +TradingPipelineDriverBase(string name, string type)
        +~TradingPipelineDriverBase()
        +Initialize() bool
        +GetManager() TradingPipelineContainerManager*
        +GetRegistry() TradingPipelineRegistry*
        +GetExplorer() TradingPipelineExplorer*
        +IsInitialized() bool
        +GetName() string
        +GetType() string
        +GetResult() PipelineResult*
        +SetupDefaultConfiguration() bool
        +Cleanup() void
        #SetResult(bool success, string message, ENUM_ERROR_LEVEL errorLevel) void
        #PreInitializeCheck() bool
        #InitializeComponents() bool*
        #SetupConfiguration() bool*
        #PostInitializeProcess() bool
    }

    %% 交易流水線驅動器
    class TradingPipelineDriver {
        -static TradingPipelineDriver* s_instance
        +GetInstance() TradingPipelineDriver*$
        +ReleaseInstance() void$
        -TradingPipelineDriver()
        #InitializeComponents() bool
        #SetupConfiguration() bool
        -InitializeManager() bool
        -InitializeRegistry() bool
        -InitializeExplorer() bool
        -CreateDefaultStageContainer(ENUM_TRADING_EVENT event, string description) bool
    }

    %% 交易流水線驅動器日誌裝飾者
    class TradingPipelineDriverLoggerDecorator {
        -TradingPipelineDriver* m_wrappedDriver
        -CFileLog* m_logger
        -bool m_enableDetailedLogging
        +TradingPipelineDriverLoggerDecorator(CFileLog* logger, bool enableDetailedLogging = true)
        +~TradingPipelineDriverLoggerDecorator()
        +Initialize() bool
        +GetManager() TradingPipelineContainerManager*
        +GetRegistry() TradingPipelineRegistry*
        +GetExplorer() TradingPipelineExplorer*
        +SetupDefaultConfiguration() bool
        +SetDetailedLogging(bool enabled) void
        +GetLogger() CFileLog*
        +IsDetailedLoggingEnabled() bool
        +GetWrappedDriver() TradingPipelineDriver*
        #InitializeComponents() bool
        #SetupConfiguration() bool
        -LogConstructor() void
        -LogDestructor() void
        -LogInfo(string message) void
        -LogDebug(string message) void
        -LogWarning(string message) void
        -LogError(string message) void
        -LogComponentStatus() void
    }

    %% 統一容器管理器
    class TradingPipelineContainerManager {
        -string m_name
        -string m_type
        -HashMap~int,TradingPipelineContainer*~ m_eventContainers
        -bool m_owned
        -bool m_isEnabled
        -bool m_executed
        -int m_maxContainers
        -PipelineResult* m_last_result
        +TradingPipelineContainerManager(string name = "TradingPipelineContainerManager", string type = "ContainerManager", bool owned = false, int maxContainers = 10)
        +~TradingPipelineContainerManager()
        +SetContainer(ENUM_TRADING_EVENT event, TradingPipelineContainer* container) bool
        +GetContainer(ENUM_TRADING_EVENT event) TradingPipelineContainer*
        +RemoveContainer(ENUM_TRADING_EVENT event) bool
        +Execute(ENUM_TRADING_EVENT event) void
        +ExecuteAll() void
        +Restore(ENUM_TRADING_EVENT event) void
        +RestoreAll() void
        +Clear() void
        +GetContainerCount() int
        +GetMaxContainers() int
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +SetEnabled(bool enabled) void
        +IsEnabled() bool
        +GetResult() PipelineResult*
        +GetStatusInfo() string

    }

    %% 流水線探索器
    class TradingPipelineExplorer {
        -TradingPipelineRegistry* m_registry
        -string m_name
        -string m_type
        -string m_description
        -PipelineResult* m_last_result
        +TradingPipelineExplorer(TradingPipelineRegistry* registry, string name = "TradingPipelineExplorer", string type = "Explorer", string description = "")
        +~TradingPipelineExplorer()
        +GetPipeline(ENUM_TRADING_STAGE stage) ITradingPipeline*
        +GetPipeline(ENUM_TRADING_EVENT event) ITradingPipeline*
        +GetAllPipelinesByStage(ENUM_TRADING_STAGE stage, ITradingPipeline* &pipelines[]) int
        +GetAllPipelinesByEvent(ENUM_TRADING_EVENT event, ITradingPipeline* &pipelines[]) int
        +HasPipelineForStage(ENUM_TRADING_STAGE stage) bool
        +HasPipelineForEvent(ENUM_TRADING_EVENT event) bool
        +GetPipelineCountByEvent(ENUM_TRADING_EVENT event) int
        +GetPipelineCountByStage(ENUM_TRADING_STAGE stage) int
        +GetTotalPipelineCount() int
        +GenerateExplorationReport() string
        +GenerateStageReport() string
        +GetName() string
        +GetType() string
        +GetDescription() string
        +GetRegistry() TradingPipelineRegistry*
        +IsValid() bool
        +GetResult() PipelineResult*

    }

    %% 流水線註冊器
    class TradingPipelineRegistry {
        -TradingPipelineContainerManager* m_manager
        -HashMap~int,TradingPipelineContainer*~ m_registeredStages
        -HashMap~int,TradingPipelineContainer*~ m_registeredEvents
        -string m_name
        -string m_type
        -int m_maxRegistrations
        -bool m_isEnabled
        -bool m_owned
        -PipelineResult* m_last_result
        +TradingPipelineRegistry(TradingPipelineContainerManager* manager, string name = "TradingPipelineRegistry", string type = "PipelineRegistry", int maxRegistrations = 50, bool owned = true)
        +~TradingPipelineRegistry()
        +Register(ENUM_TRADING_STAGE stage, TradingPipelineContainer* pipeline) bool
        +Register(ENUM_TRADING_EVENT event, TradingPipelineContainer* pipeline) bool
        +Register(TradingPipeline* pipeline) bool
        +Unregister(TradingPipeline* pipeline) bool
        +UnregisterStage(ENUM_TRADING_STAGE stage) bool
        +UnregisterEvent(ENUM_TRADING_EVENT event) bool
        +IsStageRegistered(ENUM_TRADING_STAGE stage) bool
        +IsEventRegistered(ENUM_TRADING_EVENT event) bool
        +GetRegisteredStageContainer(ENUM_TRADING_STAGE stage) TradingPipelineContainer*
        +GetRegisteredEventContainer(ENUM_TRADING_EVENT event) TradingPipelineContainer*
        +GetRegisteredStageCount() int
        +GetRegisteredEventCount() int
        +GetTotalRegistrations() int
        +Clear() void
        +SetEnabled(bool enabled) void
        +IsEnabled() bool
        +GetName() string
        +GetType() string
        +GetMaxRegistrations() int
        +IsOwned() bool
        +GetManager() TradingPipelineContainerManager*
        +GetResult() PipelineResult*

    }

    %% EA 生命週期控制器
    class TradingController {
        -TradingPipelineDriver* m_driver
        -bool m_isInitialized
        -string m_name
        -string m_type
        -PipelineResult* m_last_result
        +TradingController(TradingPipelineDriver* driver, string name = TRADING_CONTROLLER_NAME)
        +~TradingController()
        +OnInit() ENUM_INIT_RETCODE
        +OnTick() void
        +OnDeinit(int reason) void
        +GetName() string
        +GetType() string
        +IsInitialized() bool
        +GetDriver() TradingPipelineDriver*
        +GetResult() PipelineResult*
        -ExecuteInitPipeline() bool
        -ExecuteTickPipeline() bool
        -ExecuteDeinitPipeline(int reason) bool
    }

    %% 關係
    ITradingPipelineDriver <|.. TradingPipelineDriverBase : implements
    TradingPipelineDriverBase <|-- TradingPipelineDriver : extends
    TradingPipelineDriverBase <|-- TradingPipelineDriverLoggerDecorator : extends
    TradingPipelineDriver o-- TradingPipelineContainerManager : manages
    TradingPipelineDriver o-- TradingPipelineRegistry : manages
    TradingPipelineDriver o-- TradingPipelineExplorer : manages
    TradingPipelineDriverLoggerDecorator --> TradingPipelineDriver : decorates
    TradingPipelineDriverLoggerDecorator --> CFileLog : uses
    TradingController --> TradingPipelineDriver : depends on
    TradingPipelineContainerManager o-- TradingPipelineContainer : manages
    TradingPipelineContainer o-- ITradingPipeline : contains
    TradingPipelineExplorer --> TradingPipelineRegistry : queries
    TradingPipelineRegistry --> TradingPipelineContainerManager : registers
    TradingPipelineRegistry o-- TradingPipelineContainer : stores
    TradingPipelineDriverBase --> PipelineResult : uses
    TradingPipelineContainerManager --> PipelineResult : uses
    TradingPipelineExplorer --> PipelineResult : uses
    TradingPipelineRegistry --> PipelineResult : uses
    TradingPipeline --> PipelineResult : uses
    TradingController --> PipelineResult : uses
```

### 3. 枚舉、結果和工具類別圖（完整版）

```mermaid
classDiagram
    %% 枚舉類型
    class ENUM_TRADING_EVENT {
        <<enumeration>>
        TRADING_INIT
        TRADING_TICK
        TRADING_DEINIT
    }

    class ENUM_TRADING_STAGE {
        <<enumeration>>
        INIT_START
        INIT_PARAMETERS
        INIT_VARIABLES
        INIT_ENVIRONMENT
        INIT_INDICATORS
        INIT_COMPLETE
        TICK_DATA_FEED
        TICK_SIGNAL_ANALYSIS
        TICK_ORDER_MANAGEMENT
        TICK_RISK_CONTROL
        TICK_LOGGING
        DEINIT_CLEANUP
        DEINIT_SAVE_STATE
        DEINIT_COMPLETE
    }

    class ENUM_PIPELINE_STATUS {
        <<enumeration>>
        STATUS_PENDING
        STATUS_RUNNING
        STATUS_COMPLETED
        STATUS_FAILED
        STATUS_CANCELLED
    }

    class ENUM_ERROR_LEVEL {
        <<enumeration>>
        ERROR_LEVEL_INFO
        ERROR_LEVEL_WARNING
        ERROR_LEVEL_ERROR
        ERROR_LEVEL_CRITICAL
    }

    class ENUM_EXECUTION_MODE {
        <<enumeration>>
        EXECUTION_SEQUENTIAL
        EXECUTION_PARALLEL
        EXECUTION_CONDITIONAL
    }

    %% 結果類
    class PipelineResult {
        -bool m_success
        -string m_message
        -string m_source
        -datetime m_timestamp
        -ENUM_ERROR_LEVEL m_errorLevel
        +PipelineResult(bool success, string message, string source, ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO)
        +~PipelineResult()
        +IsSuccess() bool
        +GetMessage() string
        +GetSource() string
        +GetTimestamp() datetime
        +GetErrorLevel() ENUM_ERROR_LEVEL
        +ToString() string
    }

    %% 工具類
    class TradingEventUtils {
        <<utility>>
        +EventToString(ENUM_TRADING_EVENT event) string$
        +StageToString(ENUM_TRADING_STAGE stage) string$
        +StatusToString(ENUM_PIPELINE_STATUS status) string$
        +IsValidEvent(ENUM_TRADING_EVENT event) bool$
        +IsStageOfEvent(ENUM_TRADING_STAGE stage, ENUM_TRADING_EVENT event) bool$
        +GetEventStages(ENUM_TRADING_EVENT event, ENUM_TRADING_STAGE &stages[]) int$
    }

    %% 主流水線抽象類
    class MainPipeline {
        <<abstract>>
        +MainPipeline(string name = "", string type = "MainPipeline", ENUM_TRADING_STAGE stage = INIT_START, ITradingPipelineDriver* driver = NULL)
        +~MainPipeline()
        #Main() void*
        -AutoRegister() void
    }

    %% 簡單實現示例
    class SimpleTradingPipeline {
        +SimpleTradingPipeline()
    }

    %% 關係
    PipelineResult --> ENUM_ERROR_LEVEL : uses
    TradingEventUtils --> ENUM_TRADING_EVENT : uses
    TradingEventUtils --> ENUM_TRADING_STAGE : uses
    TradingEventUtils --> ENUM_PIPELINE_STATUS : uses
    TradingPipelineContainer --> PipelineResult : uses
    TradingPipeline <|-- MainPipeline : extends
```

### 4. 完整關係總覽圖

```mermaid
classDiagram
    class ITradingPipeline {
        <<interface>>
    }

    class ITradingPipelineDriver {
        <<interface>>
    }

    class TradingPipeline {
        <<abstract>>
    }

    class TradingPipelineContainerBase {
        <<abstract>>
    }

    class TradingPipelineContainer {
    }

    class EventPipeline {
    }

    class StagePipeline {
    }

    class TradingPipelineContainerManager {
    }

    class TradingPipelineExplorer {
    }

    class TradingPipelineRegistry {
    }

    class TradingPipelineDriverBase {
        <<abstract>>
    }

    class TradingPipelineDriver {
    }

    class TradingPipelineDriverLoggerDecorator {
    }

    class TradingPipelineContainerLoggerDecorator {
    }

    class TradingController {
    }

    class PipelineResult {
    }

    class MainPipeline {
        <<abstract>>
    }

    class SimpleTradingPipeline {
    }

    %% 核心關係
    ITradingPipeline <|.. TradingPipeline : implements
    ITradingPipeline <|.. TradingPipelineContainerBase : implements
    ITradingPipelineDriver <|.. TradingPipelineDriverBase : implements
    TradingPipelineDriverBase <|-- TradingPipelineDriver : extends
    TradingPipelineDriverBase <|-- TradingPipelineDriverLoggerDecorator : extends
    TradingPipelineContainerBase <|-- TradingPipelineContainer : extends
    TradingPipelineContainerBase <|-- TradingPipelineContainerLoggerDecorator : extends
    TradingPipelineContainer <|-- EventPipeline : extends
    TradingPipelineContainer <|-- StagePipeline : extends
    TradingPipeline <|-- MainPipeline : extends
    TradingPipeline <|-- SimpleTradingPipeline : extends
    TradingPipelineContainerBase o-- ITradingPipeline : contains
    TradingPipelineContainerManager o-- TradingPipelineContainer : manages
    TradingPipeline --> ITradingPipelineDriver : uses
    TradingPipelineContainerBase --> PipelineResult : uses

    %% 驅動器關係
    TradingPipelineDriver o-- TradingPipelineContainerManager : manages
    TradingPipelineDriver o-- TradingPipelineRegistry : manages
    TradingPipelineDriver o-- TradingPipelineExplorer : manages
    TradingPipelineDriverLoggerDecorator --> TradingPipelineDriver : decorates
    TradingController --> TradingPipelineDriver : depends on

    %% 裝飾者關係
    TradingPipelineDriverLoggerDecorator --> CFileLog : uses
    TradingPipelineContainerLoggerDecorator --> CFileLog : uses

    %% 其他關係
    TradingPipelineExplorer --> TradingPipelineRegistry : queries
    TradingPipelineRegistry --> TradingPipelineContainerManager : registers
    TradingPipelineExplorer --> ITradingPipeline : finds
    TradingPipelineRegistry --> TradingPipelineContainer : manages

    %% PipelineResult 關係
    TradingPipeline --> PipelineResult : uses
    TradingPipelineDriver --> PipelineResult : uses
    TradingPipelineContainerManager --> PipelineResult : uses
    TradingPipelineExplorer --> PipelineResult : uses
    TradingPipelineRegistry --> PipelineResult : uses
    TradingController --> PipelineResult : uses
```

## 設計模式

### 1. 組合模式 (Composite Pattern)

- **TradingPipelineContainer** 實現了組合模式，可以包含多個子流水線
- 客戶端可以統一處理單個流水線和容器流水線
- **TradingPipelineContainer** 直接實現 **ITradingPipeline** 介面，支持嵌套

### 2. 模板方法模式 (Template Method Pattern)

- **TradingPipeline** 定義了執行流程的骨架
- 子類實現具體的 `Main()` 方法

### 3. 統一容器管理模式 (Unified Container Management Pattern)

- **TradingPipelineContainerManager** 使用 HashMap 進行高效容器管理
- 支持無限制數量的 **TradingPipelineContainer**（受 maxContainers 限制）
- 使用容器名稱作為 key，提供 O(1) 查找性能和更直觀的 API

### 4. 策略模式 (Strategy Pattern)

- 通過不同的 **ENUM_EXECUTION_MODE** 支持不同的執行策略
- 通過 **ENUM_TRADING_STAGE** 支持不同的交易階段
- 通過 **ENUM_TRADING_EVENT** 支持事件驅動的執行

### 5. 查詢模式 (Query Pattern)

- **TradingPipelineExplorer** 實現查詢模式，提供只讀的流水線探索功能
- 支持按階段和按事件的多種查詢方式
- 提供批量查詢和統計功能

### 6. 註冊模式 (Registry Pattern)

- **TradingPipelineRegistry** 實現註冊模式，管理流水線的註冊和生命週期
- 支持智能註冊，自動處理容器和普通流水線的不同註冊邏輯
- 提供擁有權管理和最大註冊數量限制

### 7. 單例模式 (Singleton Pattern)

- **TradingPipelineDriver** 實現單例模式，確保全局只有一個驅動器實例
- 負責初始化和控制 TradingPipelineContainerManager、TradingPipelineRegistry 和 TradingPipelineExplorer 模組
- 提供統一的入口點來管理整個流水線系統

### 8. 介面隔離模式 (Interface Segregation Pattern)

- **ITradingPipeline** 介面：只包含流水線的核心操作方法，遵循介面隔離原則
- **ITradingPipelineDriver** 介面：只包含驅動器的核心管理方法，不包含 setter 方法
- **不可變設計**：介面設計支持不可變對象，確保系統穩定性
- **依賴倒置**：TradingPipeline 依賴於 ITradingPipelineDriver 抽象而非具體實現

### 9. 控制器模式 (Controller Pattern)

- **TradingController** 實現控制器模式，作為 EA 生命週期的統一入口點
- 負責協調 EA 的 OnInit、OnTick、OnDeinit 生命週期方法
- 通過構造函數注入的 TradingPipelineDriver 管理所有流水線的執行
- 提供統一的錯誤處理和狀態管理
- 採用依賴注入模式，不再使用單例模式，提高可測試性和靈活性

### 10. 裝飾者模式 (Decorator Pattern)

- **TradingPipelineDriverLoggerDecorator** 實現裝飾者模式，為驅動器添加日誌記錄功能
- **TradingPipelineContainerLoggerDecorator** 實現裝飾者模式，為容器添加日誌記錄功能
- 裝飾者類繼承自相應的基類，保持介面一致性
- 支持透明的功能擴展，不改變原有類的結構
- 提供可配置的詳細日誌記錄和錯誤追蹤功能
- 使用組合而非繼承，遵循開放/封閉原則

### 11. 模板方法模式 (Template Method Pattern) - 驅動器

- **TradingPipelineDriverBase** 實現模板方法模式，定義驅動器初始化的標準流程
- 提供鉤子方法供子類覆寫：PreInitializeCheck、InitializeComponents、SetupConfiguration、PostInitializeProcess
- 確保所有驅動器實現遵循一致的初始化順序和錯誤處理邏輯
- 支持靈活的擴展點，同時保持核心流程的穩定性

## 核心特性

### 1. 統一的架構

- 合併了 CompositePipeline 和 PipelineGroup 的功能到 **TradingPipelineContainer**
- **TradingPipelineContainer** 直接實現 **ITradingPipeline** 介面
- 減少了約 70%的重複代碼，簡化了架構層次

### 2. 高效容器管理

- **TradingPipelineContainerManager** 使用 HashMap 進行容器管理
- 支持靈活的容器數量管理（不再限制為 3 個）
- 提供 O(1) 查找性能和基於名稱的直觀 API

### 3. 階段化處理

- **ENUM_TRADING_STAGE** 定義明確的交易階段
- **TradingPipeline** 包含階段信息和驅動器引用
- 支持階段化的流水線執行

### 4. 簡化的執行模式

- 移除了容器級別的事件類型綁定，提高靈活性
- 支持統一的容器執行和重置
- 簡化了執行邏輯，減少複雜性

### 5. 類型安全

- 使用強類型的枚舉定義
- 明確的介面契約

### 6. 錯誤處理

- 統一的錯誤級別定義
- 詳細的執行結果記錄

### 7. 靈活的組織

- 統一的容器管理
- 支持動態添加和移除
- 支持嵌套容器結構

### 8. 豐富的查詢功能

- **TradingPipelineExplorer** 提供多種查詢方式
- 支持按階段、按事件的流水線查找
- 提供統計信息和報告生成功能
- 支持批量查詢和遞歸搜索

### 9. 智能註冊管理

- **TradingPipelineRegistry** 提供智能註冊功能
- 自動處理容器和普通流水線的不同註冊邏輯
- 支持擁有權管理和生命週期控制
- 提供註冊狀態查詢和管理功能

### 10. EA 生命週期管理

- **TradingController** 提供完整的 EA 生命週期管理
- 統一的 OnInit、OnTick、OnDeinit 方法實現
- 自動的錯誤處理和狀態追蹤
- 與流水線系統的無縫集成

## 使用流程

### 基本流程

1. **創建驅動器**：獲取 `TradingPipelineDriver` 單例實例
2. **創建流水線**：實現 `ITradingPipeline` 介面或繼承 `TradingPipeline`（包含階段和驅動器）
3. **創建容器**：使用 `TradingPipelineContainer` 組合多個子流水線並設置業務屬性
4. **註冊到管理器**：將 `TradingPipelineContainer` 添加到 `TradingPipelineContainerManager`
5. **執行處理**：通過管理器統一執行所有容器或按名稱執行特定容器
6. **結果檢查**：通過 `PipelineResult` 檢查執行結果

### 進階流程（使用驅動器）

1. **獲取驅動器**：獲取 `TradingPipelineDriver` 單例實例
2. **獲取組件**：通過驅動器獲取 `TradingPipelineRegistry` 和 `TradingPipelineExplorer` 實例
3. **註冊流水線**：使用註冊器的 `Register()` 方法註冊流水線到特定階段或事件
4. **查詢流水線**：使用探索器的 `GetPipeline()` 方法查詢特定階段或事件的流水線
5. **執行處理**：通過驅動器或直接通過查詢到的流水線執行處理
6. **生成報告**：使用探索器生成詳細的探索報告和統計信息

### EA 流程（使用控制器）

1. **獲取驅動器**：獲取 `TradingPipelineDriver` 實例
2. **創建控制器**：創建 `TradingController` 實例，傳入驅動器作為構造函數參數
3. **註冊流水線**：通過驅動器的註冊器註冊各種流水線到對應事件
4. **EA 初始化**：調用控制器的 `OnInit()` 方法執行初始化流水線
5. **EA 交易執行**：在每個 tick 調用控制器的 `OnTick()` 方法執行交易流水線
6. **EA 清理**：調用控制器的 `OnDeinit()` 方法執行清理流水線
7. **狀態檢查**：通過控制器檢查執行狀態和結果

## 常量定義

```mql4
// 類型常量
const string TRADING_PIPELINE_TYPE = "TradingPipeline";
const string COMPOSITE_PIPELINE_TYPE = "CompositePipeline";
const string LOGGING_PIPELINE_TYPE = "LoggingPipeline";
const string ERROR_HANDLING_PIPELINE_TYPE = "ErrorHandlingPipeline";

// 驅動器常量
#define TRADING_PIPELINE_DRIVER_NAME "TradingPipelineDriver"
#define TRADING_PIPELINE_DRIVER_TYPE "PipelineDriver"
#define DEFAULT_MAX_CONTAINERS 20
#define DEFAULT_MAX_REGISTRATIONS 100

// 控制器常量
#define TRADING_CONTROLLER_NAME "TradingController"
#define TRADING_CONTROLLER_TYPE "Controller"
#define TRADING_CONTROLLER_VERSION "1.0.0"

// 默認值常量
const int DEFAULT_MAX_PIPELINES = 100;
const int DEFAULT_MAX_RETRIES = 3;
const int DEFAULT_TIMEOUT_MS = 5000;
```

## 使用示例

```mql4
// 創建具體的主流水線
class DataFeedMainPipeline : public MainPipeline
{
public:
    DataFeedMainPipeline(string name = "DataFeedMain",
                        ITradingPipelineDriver* driver = NULL)
        : MainPipeline(name, "DataFeedMain", TICK_DATA_FEED, driver) {}

protected:
    void Main() override
    {
        // 實現數據饋送主邏輯
        Print("執行數據饋送主流水線: ", GetName(), ", 階段: ", EnumToString(GetStage()));
        // 這裡可以包含複雜的數據處理邏輯
    }
};

class SignalMainPipeline : public MainPipeline
{
public:
    SignalMainPipeline(string name = "SignalMain",
                      ITradingPipelineDriver* driver = NULL)
        : MainPipeline(name, "SignalMain", TICK_SIGNAL_ANALYSIS, driver) {}

protected:
    void Main() override
    {
        // 實現信號分析主邏輯
        Print("執行信號分析主流水線: ", GetName(), ", 階段: ", EnumToString(GetStage()));
        // 這裡可以包含複雜的信號處理邏輯
    }
};

// 創建具體的交易流水線
class DataFeedPipeline : public TradingPipeline
{
public:
    DataFeedPipeline(string name, ITradingPipelineDriver* driver = NULL)
        : TradingPipeline(name, "DataFeed", TICK_DATA_FEED, driver) {}

protected:
    void Main() override
    {
        // 實現數據饋送邏輯
        Print("執行數據饋送: ", GetName(), ", 階段: ", EnumToString(GetStage()));
    }
};

// EA 使用示例（推薦方式）
class MyEA
{
private:
    TradingController* m_controller;
    TradingPipelineDriver* m_driver;

public:
    MyEA() : m_controller(NULL), m_driver(NULL) {}

    ~MyEA()
    {
        if(m_controller != NULL)
        {
            delete m_controller;
            m_controller = NULL;
        }
        // 注意：不刪除 m_driver，因為它是單例，由 TradingPipelineDriver 自己管理
        m_driver = NULL;
    }

    ENUM_INIT_RETCODE OnInit()
    {
        // 獲取驅動器實例
        m_driver = TradingPipelineDriver::GetInstance();
        if(m_driver == NULL)
        {
            Print("[錯誤] 無法獲取 TradingPipelineDriver 實例");
            return INIT_FAILED;
        }

        // 創建控制器，傳入驅動器
        m_controller = new TradingController(m_driver, "MyEA_Controller");

        // 通過驅動器註冊流水線
        TradingPipelineRegistry* registry = m_driver.GetRegistry();

        // 創建並註冊流水線
        DataFeedMainPipeline* mainDataFeed = new DataFeedMainPipeline("主數據饋送");
        SignalMainPipeline* mainSignal = new SignalMainPipeline("主信號分析");

        registry.Register(mainDataFeed);
        registry.Register(mainSignal);

        // 調用控制器的 OnInit
        return m_controller.OnInit();
    }

    void OnTick()
    {
        if(m_controller != NULL)
        {
            m_controller.OnTick();
        }
    }

    void OnDeinit(const int reason)
    {
        if(m_controller != NULL)
        {
            m_controller.OnDeinit(reason);
        }
    }
};

// 傳統使用示例（直接使用驅動器）
void OnStart()
{
    // 1. 獲取驅動器實例
    TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();

    // 2. 獲取註冊器和管理器
    TradingPipelineRegistry* registry = driver.GetRegistry();
    TradingPipelineContainerManager* manager = driver.GetManager();

    // 3. 創建容器（使用新的 StagePipeline 類，支持階段特定功能）
    StagePipeline* tickContainer = new StagePipeline(
        "Tick處理容器",
        TICK_DATA_FEED,
        "處理每個Tick的流水線容器",
        "StagePipeline"
    );

    // 4. 註冊容器到註冊器
    registry.Register(TRADING_TICK, tickContainer);

    // 5. 創建主流水線（驅動器會自動注入）
    DataFeedMainPipeline* mainDataFeed = new DataFeedMainPipeline("主數據饋送");
    SignalMainPipeline* mainSignal = new SignalMainPipeline("主信號分析");

    // 6. 創建基本流水線
    DataFeedPipeline* dataFeed = new DataFeedPipeline("市場數據");

    // 7. 註冊主流水線到註冊器（會自動添加到對應容器）
    registry.Register(mainDataFeed);
    registry.Register(mainSignal);

    // 8. 註冊基本流水線到註冊器
    registry.Register(dataFeed);

    // 9. 通過管理器執行所有容器
    manager.ExecuteAll();

    // 10. 檢查執行結果
    Print("管理器執行狀態: ", manager.IsExecuted());
    Print("管理器中容器數量: ", manager.GetContainerCount());
    Print("容器執行狀態: ", tickContainer.IsExecuted());
    Print("容器中流水線數量: ", tickContainer.GetPipelineCount());

    // 11. 獲取探索器（通過驅動器）
    TradingPipelineExplorer* explorer = driver.GetExplorer();

    // 12. 使用探索器查詢流水線
    ITradingPipeline* foundPipeline = explorer.GetPipeline(TRADING_TICK);
    Print("找到的流水線: ", (foundPipeline != NULL) ? foundPipeline.GetName() : "未找到");

    // 13. 清理資源（驅動器會自動管理所有組件的生命週期）
    // 注意：不需要手動刪除 explorer、registry、manager，它們由驅動器管理
    // 驅動器會在程序結束時自動清理所有資源
}
```

## 優勢

1. **統一性**：合併了 CompositePipeline 和 PipelineGroup 功能，減少約 70%重複代碼
2. **簡潔性**：從 4 層架構簡化為 2 層架構，更易理解和維護
3. **高效性**：使用 HashMap 提供 O(1) 查找性能，比原有的 Vector 更高效
4. **靈活性**：移除容器級別的事件類型綁定，提高使用靈活性
5. **直觀性**：基於名稱的容器管理，API 更加直觀易用
6. **階段化**：明確的交易階段定義，支持階段化處理
7. **可維護性**：清晰的類別關係和職責分離
8. **可擴展性**：支持新的流水線類型和執行模式
9. **可測試性**：每個組件都可以獨立測試
10. **性能**：減少了不必要的抽象層和複雜邏輯，提高執行效率
11. **向後兼容**：提供完整的遷移指南和 API 對應關係
12. **豐富查詢**：TradingPipelineExplorer 提供多種查詢方式和統計功能
13. **智能註冊**：TradingPipelineRegistry 提供智能註冊和生命週期管理
14. **不可變設計**：Explorer 採用不可變設計，確保查詢操作的安全性
15. **報告生成**：支持詳細的探索報告和階段映射報告
16. **擁有權管理**：Registry 支持靈活的擁有權管理和資源控制
17. **EA 生命週期管理**：TradingController 提供完整的 EA 生命週期管理和統一入口點

## 🔄 架構變化總結

### 舊架構 (複雜)

```
PipelineGroupManager
    └── PipelineGroup (事件類型、啟用狀態)
        └── CompositePipeline (實現ITradingPipeline)
            └── ITradingPipeline (具體流水線)
```

### 新架構 (簡化 + 增強)

```
TradingController (EA 生命週期控制器)
    └── TradingPipelineDriver (單例驅動器)
        ├── TradingPipelineContainerManager (容器管理)
        │   └── TradingPipelineContainer (統一容器，實現ITradingPipeline)
        │       ├── EventPipeline (事件特定容器)
        │       ├── StagePipeline (階段特定容器)
        │       └── ITradingPipeline (具體流水線)
        ├── TradingPipelineRegistry (註冊管理)
        └── TradingPipelineExplorer (查詢和探索)
```

### 主要改進

1. **✅ 架構統一**：TradingPipelineContainer 合併了 CompositePipeline 和 PipelineGroup 功能
2. **✅ 高效管理**：TradingPipelineContainerManager 使用 HashMap 提供 O(1) 查找性能
3. **✅ 簡化設計**：移除容器級別的事件類型綁定，提高靈活性
4. **✅ 減少重複**：消除約 70%的重複功能代碼
5. **✅ 簡化層次**：從 4 層減少到 2 層結構
6. **✅ 直觀 API**：基於名稱的容器管理，更符合實際使用場景
7. **✅ 增強功能**：保留所有核心功能並提升性能
8. **🆕 豐富查詢**：TradingPipelineExplorer 提供多維度查詢和統計功能
9. **🆕 智能註冊**：TradingPipelineRegistry 提供智能註冊和生命週期管理
10. **🆕 不可變設計**：Explorer 採用不可變設計，確保查詢安全性
11. **🆕 報告生成**：支持詳細的探索報告和階段映射分析
12. **🆕 擁有權管理**：Registry 支持靈活的資源擁有權控制
13. **🆕 專門化容器**：EventPipeline 和 StagePipeline 提供事件和階段特定的功能
14. **🆕 類型安全**：通過繼承類提供更強的類型安全和專門化方法
15. **🆕 簡化探索器**：TradingPipelineExplorer 不再直接依賴 TradingPipelineContainerManager
16. **🆕 統一查詢入口**：所有查詢操作都通過 TradingPipelineRegistry 進行
17. **🆕 單例驅動器**：TradingPipelineDriver 提供統一的系統入口點和生命週期管理
18. **🆕 自動初始化**：驅動器自動初始化和管理所有核心組件的依賴關係
19. **🆕 自動注入**：TradingPipeline 構造函數自動注入驅動器實例，簡化使用
20. **🆕 預設實例**：所有流水線默認使用單例驅動器，無需手動傳遞
21. **🆕 介面資料夾**：將所有介面定義移至 interface/ 資料夾，提高代碼組織性
22. **🆕 介面抽象化**：通過 ITradingPipelineDriver 介面實現更好的依賴倒置
23. **🆕 EA 生命週期控制器**：TradingController 提供統一的 EA 入口點和生命週期管理

## 📂 介面資料夾詳細說明

### interface/ITradingPipeline.mqh

```mql4
interface ITradingPipeline
{
    void Execute();                    // 執行流水線
    string GetName();                  // 獲取名稱
    string GetType();                  // 獲取類型
    bool IsExecuted();                 // 檢查執行狀態
    void Restore();                    // 重置狀態
};
```

**設計特點**：

- 遵循介面隔離原則，只包含核心操作方法
- 不包含任何 setter 方法，支持不可變設計
- 所有流水線類型都必須實現此介面

### interface/ITradingPipelineDriver.mqh

```mql4
interface ITradingPipelineDriver
{
    bool Initialize();                                          // 初始化驅動器
    TradingPipelineContainerManager* GetManager() const;       // 獲取容器管理器
    TradingPipelineRegistry* GetRegistry() const;              // 獲取註冊器
    TradingPipelineExplorer* GetExplorer() const;              // 獲取探索器
    bool IsInitialized() const;                                // 檢查初始化狀態
    string GetName() const;                                     // 獲取名稱
    string GetType() const;                                     // 獲取類型
    bool SetupDefaultConfiguration();                          // 設置默認配置
    void Cleanup();                                             // 清理資源
};
```

**設計特點**：

- 定義驅動器的核心管理功能
- 所有方法都是 const 或操作方法，不包含狀態修改的 setter
- 支持單例模式和不可變設計
- 提供完整的生命週期管理方法

### 介面使用優勢

1. **依賴倒置**：TradingPipeline 依賴於 ITradingPipelineDriver 抽象
2. **可測試性**：可以輕鬆創建 Mock 實現進行單元測試
3. **可擴展性**：可以創建不同的驅動器實現而不影響現有代碼
4. **類型安全**：編譯時檢查介面契約的實現
5. **代碼組織**：將介面定義集中在專門的資料夾中，提高可維護性

---

## 📝 類別圖更新日誌

### 最新更新 (2024 年 12 月) - 實現對比同步

**更新內容**：

1. **PipelineResult 類別同步**：

   - ✅ 確認 `m_timestamp` 和 `m_errorLevel` 成員變數
   - ✅ 確認構造函數 `ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_INFO` 參數
   - ✅ 確認 `GetTimestamp()`, `GetErrorLevel()`, `ToString()` 方法

2. **TradingPipelineRegistry 類別同步**：

   - ✅ 確認 HashMap 值類型為 `TradingPipelineContainer*`
   - ✅ 確認 `m_owned` 成員變數存在
   - ✅ 確認構造函數 `bool owned = true` 參數
   - ✅ 確認 `maxRegistrations = 50` 默認值

3. **TradingPipelineExplorer 類別同步**：

   - ✅ 確認依賴於 `TradingPipelineRegistry*` 而非 `TradingPipelineContainerManager*`
   - ✅ 確認構造函數參數與實際實現一致

4. **MainPipeline 類別同步**：

   - ✅ 添加 `AutoRegister()` 私有方法說明
   - ✅ 反映構造函數中的自動註冊邏輯

5. **關係圖更新**：

   - ✅ 更新 TradingPipelineExplorer 與 TradingPipelineRegistry 的依賴關係
   - ✅ 添加 TradingPipelineRegistry 存儲 TradingPipelineContainer 的關係

6. **🆕 ITradingPipelineDriver 介面簡化**：
   - ✅ 移除 `ExecuteEvent(ENUM_TRADING_EVENT event)` 方法
   - ✅ 移除 `ExecuteStage(ENUM_TRADING_STAGE stage)` 方法
   - ✅ 移除 `RestoreEvent(ENUM_TRADING_EVENT event)` 方法
   - ✅ 移除 `RestoreStage(ENUM_TRADING_STAGE stage)` 方法
   - ✅ 更新 TradingPipelineDriver 實現類別
   - ✅ 更新介面資料夾說明文檔

### 🆕 最新更新 (2024 年 12 月) - PipelineResult 成員重構

**更新內容**：

1. **TradingPipeline 類別更新**：

   - ✅ 將 `PipelineResult* m_result` 重命名為 `PipelineResult* m_last_result`
   - ✅ 移除 `SetResult(bool success, string message) void` 保護方法
   - ✅ 仿效 TradingPipelineContainer 模式，直接在方法中創建 PipelineResult
   - ✅ 在 Execute() 和 Restore() 方法中直接更新 m_last_result
   - ✅ 保持 `GetResult() PipelineResult*` 公共方法

2. **TradingPipelineContainerManager 類別更新**：

   - ✅ 將 `PipelineResult* m_result` 重命名為 `PipelineResult* m_last_result`
   - ✅ 移除 `SetResult(bool success, string message) void` 私有方法
   - ✅ 在 SetContainer()、ExecuteAll()、Clear() 方法中直接創建 PipelineResult
   - ✅ 採用與 TradingPipelineContainer 一致的結果更新模式
   - ✅ 保持 `GetResult() PipelineResult*` 公共方法

3. **TradingPipelineDriver 類別更新**：

   - ✅ 將 `PipelineResult* m_result` 重命名為 `PipelineResult* m_last_result`
   - ✅ 移除 `SetResult(bool success, string message) void` 私有方法
   - ✅ 在初始化和配置方法中直接創建 PipelineResult
   - ✅ 採用統一的結果更新模式
   - ✅ 保持 `GetResult() PipelineResult*` 公共方法

4. **TradingPipelineExplorer 類別更新**：

   - ✅ 將 `PipelineResult* m_result` 重命名為 `PipelineResult* m_last_result`
   - ✅ 移除 `SetResult(bool success, string message) void` 私有方法
   - ✅ 在 GetPipeline() 方法中直接創建 PipelineResult
   - ✅ 提供詳細的查詢結果狀態反饋
   - ✅ 保持 `GetResult() PipelineResult*` 公共方法

5. **TradingPipelineRegistry 類別更新**：

   - ✅ 將 `PipelineResult* m_result` 重命名為 `PipelineResult* m_last_result`
   - ✅ 移除 `SetResult(bool success, string message) void` 私有方法
   - ✅ 在 Register()、Clear() 方法中直接創建 PipelineResult
   - ✅ 提供詳細的註冊操作狀態反饋
   - ✅ 保持 `GetResult() PipelineResult*` 公共方法

6. **設計模式統一**：

   - ✅ 所有類別現在都遵循 TradingPipelineContainer 的 PipelineResult 使用模式
   - ✅ 移除了中間的 SetResult() 方法，簡化了設計
   - ✅ 直接在操作方法中創建和設置結果，提高了性能
   - ✅ 保持了一致的命名約定（m_last_result）

7. **類別圖更新**：
   - ✅ 更新所有類別的成員變數名稱為 m_last_result
   - ✅ 移除所有 SetResult() 私有/保護方法
   - ✅ 保持 GetResult() 公共方法
   - ✅ 更新完整關係總覽圖

**驗證狀態**：✅ 已與最新代碼實現完全同步

### 🆕 最新更新 (2024 年 12 月) - TradingController 集成

**更新內容**：

1. **TradingController 類別新增**：

   - ✅ 添加 TradingController 類別到管理器和輔助類別圖
   - ✅ 完整的成員變數定義：m_driver, m_isInitialized, m_name, m_type, m_last_result
   - ✅ 完整的方法定義：OnInit(), OnTick(), OnDeinit(), 以及私有執行方法
   - ✅ 正確的構造函數和析構函數定義

2. **模組結構更新**：

   - ✅ 在模組結構中添加 TradingController.mqh
   - ✅ 在核心概念中添加 TradingController 說明
   - ✅ 更新常量定義，添加控制器相關常量

3. **關係圖更新**：

   - ✅ 添加 TradingController 與 TradingPipelineDriver 的使用關係
   - ✅ 添加 TradingController 與 PipelineResult 的使用關係
   - ✅ 更新完整關係總覽圖，包含 TradingController

4. **設計模式更新**：

   - ✅ 添加控制器模式 (Controller Pattern) 說明
   - ✅ 說明 TradingController 的職責和優勢

5. **使用流程更新**：

   - ✅ 添加 EA 流程（使用控制器）說明
   - ✅ 提供完整的 EA 使用示例，展示推薦的使用方式
   - ✅ 保留傳統使用示例作為對比

6. **架構變化更新**：

   - ✅ 更新新架構圖，將 TradingController 作為頂層入口點
   - ✅ 添加 EA 生命週期控制器的主要改進說明
   - ✅ 在優勢列表中添加 EA 生命週期管理優勢

**驗證狀態**：✅ 已與最新代碼實現完全同步

**涵蓋範圍**：

- ✅ 所有核心類別的完整成員變數和方法
- ✅ 準確的構造函數參數和默認值
- ✅ 最新的介面定義和關係
- ✅ 完整的枚舉定義和常量
- ✅ 正確的繼承和實現關係
- ✅ 統一的 PipelineResult 成員實現
- ✅ TradingController 的完整集成和文檔化
